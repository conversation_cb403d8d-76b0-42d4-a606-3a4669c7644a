# Taste Haven - Restaurant Website

A modern, interactive restaurant website built with HTML5, CSS3, and vanilla JavaScript. Features a responsive design, smooth animations, and engaging user interactions.

## 🌟 Features

### Core Functionality
- **Responsive Design**: Fully optimized for mobile, tablet, and desktop devices
- **Interactive Navigation**: Smooth scrolling navigation with mobile hamburger menu
- **Hero Section**: Full-screen hero with animated content and call-to-action buttons
- **Dynamic Menu**: Interactive menu with category filtering and image zoom
- **Reservation System**: Complete booking form with validation and confirmation
- **Photo Gallery**: Interactive gallery with lightbox functionality
- **Contact Integration**: Footer with contact details and Google Maps embed

### Technical Features
- **Semantic HTML5**: Clean, accessible markup structure
- **Modern CSS**: CSS Grid, Flexbox, custom properties, and animations
- **Vanilla JavaScript**: No external dependencies for core functionality
- **Performance Optimized**: Optimized images and efficient code structure
- **Cross-browser Compatible**: Works across all modern browsers

## 🎨 Design Features

### Visual Design
- **Modern Color Palette**: Elegant gold (#d4af37) and dark brown (#2c1810) theme
- **Typography**: Playfair Display for headings, Inter for body text
- **Smooth Animations**: CSS transitions and JavaScript-powered interactions
- **High-Quality Images**: Curated food and restaurant imagery from Unsplash

### User Experience
- **Intuitive Navigation**: Easy-to-use menu and smooth page transitions
- **Interactive Elements**: Hover effects, click animations, and visual feedback
- **Form Validation**: Real-time validation with helpful error messages
- **Accessibility**: Proper ARIA labels and keyboard navigation support

## 📁 Project Structure

```
taste-haven/
├── index.html              # Main HTML file
├── css/
│   ├── style.css          # Main stylesheet
│   └── responsive.css     # Responsive design rules
├── js/
│   ├── main.js           # Main JavaScript functionality
│   ├── menu-data.js      # Menu items data
│   └── gallery-data.js   # Gallery images data
└── README.md             # Project documentation
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional, for best experience)

### Installation
1. Clone or download the project files
2. Open `index.html` in your web browser, or
3. Serve the files using a local web server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx http-server -p 8000

# Using PHP
php -S localhost:8000
```

4. Navigate to `http://localhost:8000` in your browser

## 📱 Responsive Breakpoints

- **Desktop**: 1024px and above
- **Tablet**: 768px - 1023px
- **Mobile**: 320px - 767px

## 🎯 Key Sections

### 1. Hero Section
- Full-screen background with overlay
- Animated title and subtitle
- Call-to-action buttons for reservations and menu
- Scroll indicator with smooth scrolling

### 2. Menu Section
- Interactive category filtering (All, Appetizers, Main Dishes, Desserts, Beverages)
- Hover effects on menu items
- Image zoom functionality
- Detailed descriptions and pricing

### 3. Reservation Form
- Complete booking form with validation
- Date picker with future date restriction
- Time slot selection
- Guest count selection
- Special requests textarea
- Animated confirmation modal

### 4. Gallery Section
- Grid layout with responsive design
- Lightbox functionality for image viewing
- Keyboard navigation support
- Image categories (food, interior, beverages, etc.)

### 5. Footer
- Contact information and hours
- Social media links
- Google Maps integration
- Professional layout with multiple sections

## 🛠️ Customization

### Colors
Update CSS custom properties in `css/style.css`:
```css
:root {
    --primary-color: #d4af37;
    --secondary-color: #2c1810;
    --accent-color: #ff6b35;
    /* ... other colors */
}
```

### Menu Items
Edit `js/menu-data.js` to add/modify menu items:
```javascript
const menuData = [
    {
        id: 1,
        name: "Dish Name",
        description: "Dish description",
        price: "$XX",
        category: "category",
        image: "image-url"
    }
    // ... more items
];
```

### Gallery Images
Update `js/gallery-data.js` to change gallery images:
```javascript
const galleryData = [
    {
        id: 1,
        src: "image-url",
        alt: "Image description",
        category: "category"
    }
    // ... more images
];
```

## 🔧 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

For support, email <EMAIL> or create an issue in the repository.

## 🙏 Acknowledgments

- Images provided by [Unsplash](https://unsplash.com)
- Icons by [Font Awesome](https://fontawesome.com)
- Fonts by [Google Fonts](https://fonts.google.com)

---

**Taste Haven** - Where culinary artistry meets exceptional dining ✨
