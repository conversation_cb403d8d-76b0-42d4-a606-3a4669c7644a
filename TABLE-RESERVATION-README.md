# Interactive Table Reservation System - Taste Haven

A photorealistic, visually engaging and interactive table reservation system featuring a detailed top-view 2D restaurant layout that looks like a real restaurant image. Customers can select and reserve their preferred tables with real-time visual feedback in an immersive environment.

## 🎯 Features

### Photorealistic Restaurant Layout
- **2D Top-View Design**: Detailed, realistic restaurant floor plan with wood grain flooring
- **Multiple Dining Areas**: Bar, Main Dining, Window Side, Private Dining with authentic textures
- **Visual Elements**: Kitchen, Restrooms, Entrance with realistic styling and decorative elements
- **Realistic Tables**: Wood-textured tables with table settings, chairs, and proper shadows
- **Ambient Lighting**: Chandelier lighting effects and ambient glow throughout the restaurant
- **Decorative Elements**: Plants, carpets, host station, and atmospheric details
- **Color-Coded Status**: Green (Available), Gold (Selected), Red (Reserved), Gray (Occupied)

### Interactive Table Selection
- **Click-to-Select**: Simply click on any available table
- **Real-Time Feedback**: Immediate visual response and status updates
- **Table Information**: Hover tooltips showing table details
- **Smart Validation**: Prevents selection of unavailable tables

### Comprehensive Reservation System
- **Modal-Based Form**: Clean, focused reservation interface
- **Form Validation**: Real-time validation with helpful error messages
- **Party Size Matching**: Automatically limits party size to table capacity
- **Date/Time Selection**: Future date validation and time slot options
- **Confirmation System**: Detailed reservation confirmation with all details

### Data Persistence
- **Local Storage**: Reservations persist across browser sessions
- **Status Management**: Tables remain reserved until manually cleared
- **Reservation History**: All reservation data stored locally

## 🏗️ Technical Implementation

### File Structure
```
├── table-reservation.html     # Main reservation page
├── css/
│   └── table-reservation.css  # Styling for reservation system
├── js/
│   └── table-reservation.js   # Interactive functionality
└── TABLE-RESERVATION-README.md
```

### Technologies Used
- **HTML5**: Semantic structure and accessibility
- **CSS3**: Grid layout, animations, responsive design
- **Vanilla JavaScript**: Interactive functionality, form validation
- **Local Storage**: Data persistence

## 🎨 Restaurant Layout Design

### Dining Areas
1. **Bar Area (4 tables)**: B1-B4, 2 seats each
2. **Main Dining (11 tables)**: Tables 1-11, 2-6 seats
3. **Window Side (4 tables)**: W1-W4, 2-4 seats with window view
4. **Private Dining (2 tables)**: P1-P2, 8-10 seats with private rooms

### Table Types
- **Round Tables**: 4-6 person capacity
- **Square Tables**: 2 person capacity  
- **Bar Tables**: 2 person capacity at bar counter
- **Window Tables**: 2-4 person capacity with scenic view
- **Private Tables**: 8-10 person capacity in private rooms

## 🔧 Key Features

### Table Selection Logic
```javascript
// Example of table selection handling
function handleTableClick(event) {
    const table = event.currentTarget;
    const tableId = table.getAttribute('data-table');
    
    // Check availability
    if (table.classList.contains('reserved') || table.classList.contains('occupied')) {
        showUnavailableMessage(table);
        return;
    }
    
    // Select table and show information
    selectTable(table, tableId);
}
```

### Form Validation
- **Required Fields**: Name, phone, email, date, time, party size
- **Email Format**: Valid email address validation
- **Phone Format**: International phone number support
- **Date Validation**: Future dates only
- **Party Size**: Limited to table capacity

### Visual Feedback
- **Hover Effects**: Table information tooltips
- **Selection Animation**: Pulsing effect for selected tables
- **Status Colors**: Intuitive color coding system
- **Smooth Transitions**: CSS animations for all interactions

## 📱 Responsive Design

### Breakpoints
- **Desktop**: 1024px+ (Grid layout with 3 columns)
- **Tablet**: 768px-1023px (Stacked layout)
- **Mobile**: 320px-767px (Single column, touch-optimized)

### Mobile Optimizations
- **Touch-Friendly**: Larger touch targets for mobile devices
- **Simplified Layout**: Stacked areas for better mobile viewing
- **Optimized Forms**: Mobile-friendly form inputs
- **Gesture Support**: Touch interactions for table selection

## 🚀 Usage Instructions

### For Customers
1. **View Layout**: See the restaurant floor plan with all available tables
2. **Select Table**: Click on any green (available) table
3. **Review Details**: Check table information and capacity
4. **Make Reservation**: Fill out the reservation form
5. **Confirm**: Review and confirm your reservation

### For Developers
1. **Customize Layout**: Modify the CSS grid in `restaurant-floor` class
2. **Add Tables**: Add new table elements with appropriate data attributes
3. **Update Styling**: Modify colors and animations in CSS
4. **Extend Functionality**: Add new features in JavaScript

## 🎛️ Customization Options

### Adding New Tables
```html
<div class="table round-table available" 
     data-table="NEW1" 
     data-seats="4" 
     data-type="Round Table"
     data-special="Special Feature">
    <span class="table-number">NEW1</span>
    <div class="seats">4</div>
</div>
```

### Modifying Table Status
```javascript
// Change table status programmatically
const table = document.querySelector('[data-table="1"]');
table.classList.remove('available');
table.classList.add('reserved');
```

### Custom Validation Rules
```javascript
// Add custom validation in validateField function
if (field.name === 'custom-field') {
    // Custom validation logic
    if (!customValidation(field.value)) {
        isValid = false;
        message = 'Custom error message';
    }
}
```

## 🔒 Data Management

### Local Storage Structure
```javascript
{
    "tableId": {
        "tableNumber": "1",
        "name": "John Doe",
        "phone": "+1234567890",
        "email": "<EMAIL>",
        "date": "2024-12-25",
        "time": "19:00",
        "partySize": "4",
        "status": "reserved",
        "timestamp": "2024-12-20T10:30:00.000Z"
    }
}
```

### Clearing Reservations
```javascript
// Clear all reservations
localStorage.removeItem('tableReservations');

// Clear specific table
const reservations = JSON.parse(localStorage.getItem('tableReservations')) || {};
delete reservations['tableId'];
localStorage.setItem('tableReservations', JSON.stringify(reservations));
```

## 🌟 Advanced Features

### Tooltip System
- **Hover Information**: Table details on mouse hover
- **Dynamic Content**: Shows table type, capacity, and special features
- **Responsive**: Adapts to screen size and touch devices

### Animation System
- **Selection Pulse**: Animated pulse effect for selected tables
- **Smooth Transitions**: CSS transitions for all state changes
- **Modal Animations**: Slide-in effects for modals

### Error Handling
- **Unavailable Tables**: Clear feedback for reserved/occupied tables
- **Form Validation**: Real-time validation with helpful messages
- **Network Resilience**: Graceful handling of storage errors

## 🔧 Browser Support

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile Browsers**: iOS Safari, Chrome Mobile

## 📄 License

This project is part of the Taste Haven restaurant website and follows the same MIT License.

---

**Interactive Table Reservation System** - Making restaurant reservations visual and intuitive! 🍽️✨
