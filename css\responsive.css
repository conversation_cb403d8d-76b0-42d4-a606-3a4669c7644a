/* ===== RESPONSIVE DESIGN ===== */

/* Large Tablets and Small Desktops */
@media screen and (max-width: 1024px) {
    .container {
        padding: 0 30px;
    }
    
    .hero-buttons {
        gap: 0.5rem;
    }
    
    .menu-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
    }
    
    .reservation-content {
        gap: 3rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablets */
@media screen and (max-width: 768px) {
    :root {
        --section-padding: 60px 0;
    }
    
    .container {
        padding: 0 20px;
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: rgba(26, 26, 26, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
        gap: 1rem;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    /* Hero Section */
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .btn {
        width: 200px;
    }
    
    /* Menu Section */
    .menu-filters {
        gap: 0.5rem;
    }
    
    .filter-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .menu-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    /* Reservation Section */
    .reservation-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    /* Gallery */
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .social-links {
        justify-content: center;
    }
    
    /* Modal */
    .modal-content {
        margin: 20% auto;
        padding: 1.5rem;
        width: 95%;
    }
    
    /* Lightbox */
    .lightbox-image {
        max-width: 95%;
        max-height: 80%;
    }
    
    .lightbox-close {
        top: 10px;
        right: 15px;
        font-size: 1.5rem;
    }
    
    .lightbox-nav {
        padding: 0 10px;
    }
}

/* Mobile Phones */
@media screen and (max-width: 480px) {
    :root {
        --section-padding: 40px 0;
        --container-padding: 0 15px;
    }
    
    .container {
        padding: var(--container-padding);
    }
    
    /* Typography */
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
    
    /* Navigation */
    .nav-container {
        padding: 0 15px;
    }
    
    .nav-logo h2 {
        font-size: 1.5rem;
    }
    
    .navbar {
        padding: 0.8rem 0;
    }
    
    /* Hero Section */
    .hero-content {
        padding: 0 15px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-buttons {
        gap: 0.8rem;
    }
    
    .btn {
        width: 180px;
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    /* Section Headers */
    .section-header {
        margin-bottom: 2rem;
    }
    
    .section-header p {
        font-size: 1rem;
    }
    
    /* Menu Section */
    .menu-filters {
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
    }
    
    .filter-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 80px;
    }
    
    .menu-item-content {
        padding: 1rem;
    }
    
    .menu-item-title {
        font-size: 1.1rem;
    }
    
    .menu-item-price {
        font-size: 1rem;
    }
    
    .menu-item-description {
        font-size: 0.85rem;
    }
    
    /* Reservation Section */
    .reservation-form {
        padding: 1.5rem;
    }
    
    .reservation-info {
        padding: 1.5rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 0.9rem;
    }
    
    .info-item {
        margin-bottom: 1.5rem;
    }
    
    .info-item h4 {
        font-size: 0.9rem;
    }
    
    .info-item p {
        font-size: 0.8rem;
    }
    
    /* Gallery */
    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .gallery-item {
        aspect-ratio: 4/3;
    }
    
    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }
    
    .footer-content {
        gap: 1.5rem;
    }
    
    .footer-section h3,
    .footer-section h4 {
        font-size: 1.1rem;
    }
    
    .footer-section p {
        font-size: 0.9rem;
    }
    
    .contact-item {
        margin-bottom: 0.8rem;
    }
    
    .hours-item {
        font-size: 0.9rem;
    }
    
    .social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
    
    .map-container {
        height: 150px;
    }
    
    /* Modal */
    .modal-content {
        margin: 30% auto;
        padding: 1rem;
        width: 90%;
    }
    
    .modal-header i {
        font-size: 2rem;
    }
    
    .modal-header h3 {
        font-size: 1.2rem;
    }
    
    .modal-body p {
        font-size: 0.9rem;
    }
    
    /* Lightbox */
    .lightbox-image {
        max-width: 90%;
        max-height: 70%;
    }
    
    .lightbox-close {
        top: 5px;
        right: 10px;
        font-size: 1.2rem;
    }
    
    .lightbox-prev,
    .lightbox-next {
        padding: 8px 12px;
        font-size: 1rem;
    }
}

/* Extra Small Devices */
@media screen and (max-width: 320px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 0.9rem;
    }
    
    .btn {
        width: 160px;
        padding: 8px 16px;
        font-size: 0.8rem;
    }
    
    .menu-item-content {
        padding: 0.8rem;
    }
    
    .reservation-form,
    .reservation-info {
        padding: 1rem;
    }
    
    .modal-content {
        margin: 40% auto;
        padding: 0.8rem;
    }
}

/* Landscape Orientation for Mobile */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .hero {
        height: 100vh;
        min-height: 500px;
    }
    
    .hero-content {
        transform: scale(0.9);
    }
    
    .scroll-indicator {
        bottom: 15px;
    }
}

/* High DPI Displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
    .hero-background {
        background-image: linear-gradient(135deg, rgba(212, 175, 55, 0.8), rgba(44, 24, 16, 0.8)),
                          url('https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=4140&q=80');
    }
}

/* Print Styles */
@media print {
    .navbar,
    .hero-buttons,
    .scroll-indicator,
    .menu-filters,
    .reservation-form,
    .modal,
    .lightbox {
        display: none !important;
    }
    
    .hero {
        height: auto;
        padding: 2rem 0;
    }
    
    .hero-background {
        display: none;
    }
    
    .hero-content {
        color: var(--text-dark) !important;
    }
    
    .section-header,
    .menu-item,
    .footer-section {
        break-inside: avoid;
    }
}
