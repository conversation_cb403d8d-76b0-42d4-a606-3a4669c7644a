/* ===== TABLE RESERVATION SYSTEM STYLES ===== */

/* Header Section */
.reservation-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
}

.reservation-header h1 {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.reservation-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Legend Section */
.legend-section {
    background: var(--bg-light);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.legend {
    text-align: center;
}

.legend h3 {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.legend-items {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-color.available {
    background: #4CAF50;
}

.legend-color.selected {
    background: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
}

.legend-color.reserved {
    background: #f44336;
}

.legend-color.occupied {
    background: #9E9E9E;
}

/* Restaurant Layout */
.restaurant-layout-section {
    padding: 3rem 0;
    background: var(--text-white);
}

.layout-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.restaurant-floor {
    position: relative;
    background: linear-gradient(45deg, #f8f8f8 25%, transparent 25%), 
                linear-gradient(-45deg, #f8f8f8 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f8f8f8 75%), 
                linear-gradient(-45deg, transparent 75%, #f8f8f8 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    border: 3px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    min-height: 600px;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: auto 1fr auto;
    gap: 1rem;
    box-shadow: var(--shadow);
}

/* Area Labels */
.area-label {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary-color);
    color: var(--text-white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
}

/* Entrance */
.entrance {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #8BC34A, #689F38);
    color: white;
    border-radius: 10px;
    padding: 1rem;
    font-weight: 600;
    box-shadow: var(--shadow);
}

.entrance i {
    font-size: 1.5rem;
}

/* Bar Area */
.bar-area {
    grid-column: 1;
    grid-row: 2;
    position: relative;
    background: linear-gradient(135deg, #795548, #5D4037);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    box-shadow: var(--shadow);
}

.bar-counter {
    background: linear-gradient(135deg, #3E2723, #1B0000);
    height: 40px;
    border-radius: 20px;
    margin-bottom: 1rem;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Main Dining Area */
.main-dining {
    grid-column: 2;
    grid-row: 2;
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 1rem;
    box-shadow: var(--shadow);
}

/* Window Side */
.window-side {
    grid-column: 3;
    grid-row: 2;
    position: relative;
    background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    box-shadow: var(--shadow);
}

.window-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #1976D2;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.window-decoration i {
    font-size: 1.2rem;
}

/* Private Dining */
.private-dining {
    grid-column: 1;
    grid-row: 3;
    position: relative;
    background: linear-gradient(135deg, #F3E5F5, #E1BEE7);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    gap: 1rem;
    box-shadow: var(--shadow);
}

/* Kitchen Area */
.kitchen-area {
    grid-column: 2;
    grid-row: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #FF9800, #F57C00);
    color: white;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: var(--shadow);
}

.kitchen-area i {
    font-size: 1.5rem;
}

/* Restrooms */
.restrooms {
    grid-column: 3;
    grid-row: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #607D8B, #455A64);
    color: white;
    border-radius: 10px;
    font-weight: 600;
    box-shadow: var(--shadow);
}

.restrooms i {
    font-size: 1.5rem;
}

/* Table Styles */
.table {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 60px;
    border: 2px solid transparent;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.table-number {
    font-weight: 700;
    font-size: 1rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.seats {
    font-size: 0.7rem;
    color: white;
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 10px;
    margin-top: 2px;
}

/* Table Types */
.round-table {
    border-radius: 50%;
    width: 70px;
    height: 70px;
}

.square-table {
    border-radius: 8px;
    width: 60px;
    height: 60px;
}

.bar-table {
    border-radius: 15px;
    width: 100%;
    height: 50px;
    margin-bottom: 0.5rem;
}

.window-table {
    border-radius: 12px;
    width: 100%;
    height: 60px;
    margin-bottom: 0.5rem;
}

.private-table {
    border-radius: 15px;
    width: 100%;
    height: 80px;
}

/* Table Status Colors */
.table.available {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.table.selected {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
    animation: pulse 2s infinite;
}

.table.reserved {
    background: linear-gradient(135deg, #f44336, #d32f2f);
    cursor: not-allowed;
}

.table.occupied {
    background: linear-gradient(135deg, #9E9E9E, #757575);
    cursor: not-allowed;
}

.table.reserved:hover,
.table.occupied:hover {
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Selected Table Info */
.selected-table-info {
    background: var(--bg-light);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 2rem;
    animation: slideInUp 0.3s ease;
}

.selected-table-info h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.table-details {
    margin-bottom: 1.5rem;
}

.table-details p {
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.table-details strong {
    color: var(--text-dark);
}

/* Modal Styles */
.reservation-modal .modal-content {
    max-width: 600px;
    width: 90%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-dark);
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-dark);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Confirmation Modal */
.confirmation-modal .modal-header {
    text-align: center;
    border: none;
    padding-bottom: 0;
}

.confirmation-modal .modal-header i {
    color: #4CAF50;
    font-size: 3rem;
    margin-bottom: 1rem;
}

.confirmation-details {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.confirmation-details h4 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-row .label {
    font-weight: 600;
    color: var(--text-dark);
}

.detail-row .value {
    color: var(--text-light);
}

.confirmation-message {
    text-align: center;
    color: var(--text-light);
    font-style: italic;
}

/* Tooltip */
.table-tooltip {
    position: absolute;
    background: var(--bg-dark);
    color: var(--text-white);
    padding: 0.8rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    max-width: 200px;
}

.table-tooltip.show {
    opacity: 1;
}

.tooltip-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.tooltip-content p {
    margin: 0;
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN FOR TABLE RESERVATION ===== */

/* Tablets */
@media screen and (max-width: 768px) {
    .reservation-header {
        padding: 100px 0 40px;
    }

    .reservation-header h1 {
        font-size: 2.5rem;
    }

    .legend-items {
        gap: 1rem;
    }

    .legend-item {
        font-size: 0.9rem;
    }

    .restaurant-floor {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto auto auto;
        gap: 1rem;
        padding: 1.5rem;
        min-height: auto;
    }

    .entrance {
        grid-column: 1;
        grid-row: 1;
    }

    .bar-area {
        grid-column: 1;
        grid-row: 2;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .main-dining {
        grid-column: 1;
        grid-row: 3;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
    }

    .window-side {
        grid-column: 1;
        grid-row: 4;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .private-dining {
        grid-column: 1;
        grid-row: 5;
        flex-direction: column;
        align-items: center;
    }

    .kitchen-area {
        grid-column: 1;
        grid-row: 6;
    }

    .restrooms {
        grid-column: 1;
        grid-row: 7;
    }

    .table {
        min-height: 50px;
    }

    .round-table {
        width: 60px;
        height: 60px;
    }

    .square-table {
        width: 50px;
        height: 50px;
    }

    .bar-table,
    .window-table {
        width: 80px;
        height: 45px;
        margin: 0.25rem;
    }

    .private-table {
        width: 120px;
        height: 60px;
        margin: 0.5rem 0;
    }

    .selected-table-info {
        padding: 1rem;
    }

    .reservation-modal .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Mobile Phones */
@media screen and (max-width: 480px) {
    .reservation-header {
        padding: 90px 0 30px;
    }

    .reservation-header h1 {
        font-size: 2rem;
    }

    .reservation-header p {
        font-size: 1rem;
    }

    .legend-section {
        padding: 1.5rem 0;
    }

    .legend-items {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .legend-item {
        font-size: 0.85rem;
    }

    .restaurant-floor {
        padding: 1rem;
        gap: 0.8rem;
    }

    .area-label {
        font-size: 0.7rem;
        padding: 2px 8px;
    }

    .entrance,
    .kitchen-area,
    .restrooms {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .entrance i,
    .kitchen-area i,
    .restrooms i {
        font-size: 1.2rem;
    }

    .bar-area,
    .window-side,
    .private-dining {
        padding: 0.8rem;
    }

    .main-dining {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
        gap: 0.5rem;
        padding: 0.8rem;
    }

    .table {
        min-height: 40px;
    }

    .table-number {
        font-size: 0.8rem;
    }

    .seats {
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    .round-table {
        width: 45px;
        height: 45px;
    }

    .square-table {
        width: 40px;
        height: 40px;
    }

    .bar-table,
    .window-table {
        width: 60px;
        height: 35px;
        margin: 0.2rem;
    }

    .private-table {
        width: 100px;
        height: 50px;
        margin: 0.3rem 0;
    }

    .selected-table-info {
        padding: 0.8rem;
        margin-top: 1rem;
    }

    .selected-table-info h3 {
        font-size: 1.1rem;
    }

    .table-details p {
        font-size: 0.9rem;
    }

    .reservation-modal .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 0.9rem;
    }

    .confirmation-modal .modal-content {
        width: 95%;
        margin: 15% auto;
    }

    .confirmation-modal .modal-header i {
        font-size: 2.5rem;
    }

    .confirmation-details {
        padding: 1rem;
    }

    .detail-row {
        font-size: 0.9rem;
    }

    .table-tooltip {
        max-width: 150px;
        padding: 0.6rem;
    }

    .tooltip-content h4 {
        font-size: 0.8rem;
    }

    .tooltip-content p {
        font-size: 0.7rem;
    }
}

/* Extra Small Devices */
@media screen and (max-width: 320px) {
    .reservation-header h1 {
        font-size: 1.8rem;
    }

    .restaurant-floor {
        padding: 0.8rem;
    }

    .main-dining {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(11, 1fr);
    }

    .round-table {
        width: 40px;
        height: 40px;
    }

    .square-table {
        width: 35px;
        height: 35px;
    }

    .bar-table,
    .window-table {
        width: 50px;
        height: 30px;
    }

    .private-table {
        width: 80px;
        height: 40px;
    }

    .table-number {
        font-size: 0.7rem;
    }

    .seats {
        font-size: 0.5rem;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .table {
        min-height: 50px;
        min-width: 50px;
    }

    .table:hover {
        transform: none;
    }

    .table:active {
        transform: scale(0.95);
    }

    .round-table {
        width: 55px;
        height: 55px;
    }

    .square-table {
        width: 50px;
        height: 50px;
    }

    .bar-table,
    .window-table {
        height: 50px;
    }

    .table-tooltip {
        display: none;
    }
}

/* Landscape Orientation for Mobile */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .reservation-header {
        padding: 80px 0 20px;
    }

    .restaurant-floor {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: auto 1fr auto;
        min-height: 400px;
    }

    .entrance {
        grid-column: 2;
        grid-row: 1;
    }

    .bar-area {
        grid-column: 1;
        grid-row: 2;
    }

    .main-dining {
        grid-column: 2;
        grid-row: 2;
    }

    .window-side {
        grid-column: 3;
        grid-row: 2;
    }

    .private-dining {
        grid-column: 1;
        grid-row: 3;
    }

    .kitchen-area {
        grid-column: 2;
        grid-row: 3;
    }

    .restrooms {
        grid-column: 3;
        grid-row: 3;
    }
}
