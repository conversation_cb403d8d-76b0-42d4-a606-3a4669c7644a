/* ===== TABLE RESERVATION SYSTEM STYLES ===== */

/* Header Section */
.reservation-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--text-white);
    padding: 120px 0 60px;
    text-align: center;
}

.reservation-header h1 {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.reservation-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Legend Section */
.legend-section {
    background: var(--bg-light);
    padding: 2rem 0;
    border-bottom: 1px solid var(--border-color);
}

.legend {
    text-align: center;
}

.legend h3 {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.legend-items {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-color.available {
    background: #4CAF50;
}

.legend-color.selected {
    background: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.3);
}

.legend-color.reserved {
    background: #f44336;
}

.legend-color.occupied {
    background: #9E9E9E;
}

/* Restaurant Layout */
.restaurant-layout-section {
    padding: 3rem 0;
    background: var(--text-white);
}

.layout-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.restaurant-floor {
    position: relative;
    background:
        /* Wood floor pattern with realistic grain */
        linear-gradient(90deg,
            #8B4513 0%,
            #A0522D 15%,
            #CD853F 30%,
            #D2B48C 45%,
            #A0522D 60%,
            #8B4513 75%,
            #654321 90%,
            #8B4513 100%
        ),
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 12px,
            rgba(139, 69, 19, 0.2) 12px,
            rgba(139, 69, 19, 0.2) 14px,
            transparent 14px,
            transparent 26px,
            rgba(101, 67, 33, 0.3) 26px,
            rgba(101, 67, 33, 0.3) 28px
        ),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 80px,
            rgba(139, 69, 19, 0.1) 80px,
            rgba(139, 69, 19, 0.1) 82px
        );
    background-size: 100% 100%, 100% 28px, 82px 100%;
    border: 12px solid #654321;
    border-radius: 25px;
    padding: 3rem;
    min-height: 700px;
    display: grid;
    grid-template-columns: 300px 1fr 280px;
    grid-template-rows: 80px 1fr 120px;
    gap: 2rem;
    box-shadow:
        0 0 0 6px #8B4513,
        0 0 0 8px var(--primary-color),
        0 15px 40px rgba(0, 0, 0, 0.4),
        inset 0 0 60px rgba(139, 69, 19, 0.15),
        inset 0 0 120px rgba(212, 175, 55, 0.05);
    overflow: hidden;
}

.restaurant-floor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        /* Ambient lighting effects */
        radial-gradient(circle at 20% 30%, rgba(212, 175, 55, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 80% 70%, rgba(212, 175, 55, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 50% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 35%),
        radial-gradient(circle at 30% 80%, rgba(212, 175, 55, 0.1) 0%, transparent 45%),
        radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.06) 0%, transparent 30%),
        /* Chandelier light */
        radial-gradient(circle at 50% 15%, rgba(212, 175, 55, 0.3) 0%, rgba(212, 175, 55, 0.1) 20%, transparent 40%);
    pointer-events: none;
    z-index: 2;
    animation: ambientGlow 8s ease-in-out infinite;
}

.restaurant-floor::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        /* Subtle shadow patterns */
        repeating-conic-gradient(
            from 0deg at 50% 50%,
            transparent 0deg,
            rgba(0, 0, 0, 0.02) 45deg,
            transparent 90deg
        );
    pointer-events: none;
    z-index: 1;
}

@keyframes ambientGlow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Decorative Carpet */
.carpet {
    position: absolute;
    z-index: 0;
    border-radius: 15px;
    opacity: 0.7;
}

.main-carpet {
    top: 10%;
    left: 10%;
    width: 80%;
    height: 80%;
    background:
        repeating-conic-gradient(
            from 0deg at 50% 50%,
            #8B0000 0deg,
            #A0522D 30deg,
            #8B0000 60deg,
            #654321 90deg
        ),
        radial-gradient(circle at center, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
    background-size: 40px 40px, 100% 100%;
    border: 3px solid var(--primary-color);
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Area Labels */
.area-label {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary-color);
    color: var(--text-white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
}

/* Entrance */
.entrance {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background:
        linear-gradient(135deg, #2C1810 0%, #3E2723 50%, #2C1810 100%),
        radial-gradient(circle at center, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
    color: var(--primary-color);
    border-radius: 15px;
    padding: 1rem;
    font-weight: 600;
    box-shadow:
        inset 0 2px 10px rgba(212, 175, 55, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--primary-color);
    position: relative;
}

.entrance::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 10px;
    background: var(--primary-color);
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.entrance i {
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Bar Area */
.bar-area {
    grid-column: 1;
    grid-row: 2;
    position: relative;
    background:
        linear-gradient(135deg, #2C1810 0%, #3E2723 50%, #2C1810 100%),
        radial-gradient(circle at 30% 30%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.3),
        0 8px 25px rgba(0, 0, 0, 0.2);
    border: 2px solid #654321;
}

.bar-counter {
    background:
        linear-gradient(135deg, #1a0e0a 0%, #2d1b13 50%, #1a0e0a 100%),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 20px,
            rgba(212, 175, 55, 0.1) 20px,
            rgba(212, 175, 55, 0.1) 22px
        );
    height: 50px;
    border-radius: 25px;
    margin-bottom: 1rem;
    box-shadow:
        inset 0 4px 8px rgba(0, 0, 0, 0.5),
        0 2px 10px rgba(212, 175, 55, 0.3);
    border: 3px solid var(--primary-color);
    position: relative;
}

.bar-counter::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 10px;
    right: 10px;
    height: 8px;
    background: linear-gradient(90deg, var(--primary-color), transparent, var(--primary-color));
    border-radius: 4px;
    opacity: 0.6;
}

/* Main Dining Area */
.main-dining {
    grid-column: 2;
    grid-row: 2;
    position: relative;
    background:
        linear-gradient(45deg, #f5f5f0 25%, transparent 25%),
        linear-gradient(-45deg, #f5f5f0 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #f5f5f0 75%),
        linear-gradient(-45deg, transparent 75%, #f5f5f0 75%),
        linear-gradient(135deg, #faf8f3 0%, #f0ede6 100%);
    background-size: 30px 30px, 30px 30px, 30px 30px, 30px 30px, 100% 100%;
    background-position: 0 0, 0 15px, 15px -15px, -15px 0px, 0 0;
    border-radius: 20px;
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2rem;
    box-shadow:
        inset 0 0 30px rgba(212, 175, 55, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.15);
    border: 3px solid #e8e2d4;
    overflow: visible;
}

.main-dining::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-color), transparent, var(--primary-color));
    border-radius: 25px;
    z-index: -1;
    opacity: 0.3;
}

/* Window Side */
.window-side {
    grid-column: 3;
    grid-row: 2;
    position: relative;
    background:
        linear-gradient(90deg,
            #87CEEB 0%,
            #B0E0E6 20%,
            #F0F8FF 40%,
            #B0E0E6 60%,
            #87CEEB 100%
        ),
        repeating-linear-gradient(
            0deg,
            transparent,
            transparent 40px,
            rgba(255, 255, 255, 0.3) 40px,
            rgba(255, 255, 255, 0.3) 44px
        );
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    box-shadow:
        inset 0 0 30px rgba(135, 206, 235, 0.3),
        0 8px 25px rgba(0, 0, 0, 0.15);
    border: 3px solid #4682B4;
    overflow: hidden;
}

.window-side::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background:
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 30px,
            rgba(255, 255, 255, 0.4) 30px,
            rgba(255, 255, 255, 0.4) 34px
        );
    z-index: 1;
    pointer-events: none;
}

.window-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: #1976D2;
    font-weight: 600;
    margin-bottom: 1rem;
    z-index: 2;
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    padding: 0.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.window-decoration i {
    font-size: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.window-decoration::after {
    content: '🌅 City View 🌅';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: #1976D2;
    white-space: nowrap;
}

/* Private Dining */
.private-dining {
    grid-column: 1;
    grid-row: 3;
    position: relative;
    background:
        linear-gradient(135deg, #2C1810 0%, #4A2C2A 50%, #2C1810 100%),
        radial-gradient(circle at center, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    gap: 1.5rem;
    box-shadow:
        inset 0 0 20px rgba(212, 175, 55, 0.2),
        0 8px 25px rgba(0, 0, 0, 0.3);
    border: 3px solid var(--primary-color);
    overflow: hidden;
}

.private-dining::before {
    content: '🏛️ VIP DINING 🏛️';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

/* Kitchen Area */
.kitchen-area {
    grid-column: 2;
    grid-row: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background:
        linear-gradient(135deg, #C0392B 0%, #E74C3C 50%, #C0392B 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(255, 255, 255, 0.1) 10px,
            rgba(255, 255, 255, 0.1) 12px
        );
    color: white;
    border-radius: 15px;
    font-weight: 600;
    box-shadow:
        inset 0 0 20px rgba(255, 255, 255, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.3);
    border: 3px solid #A93226;
    position: relative;
    overflow: hidden;
}

.kitchen-area::before {
    content: '🔥';
    position: absolute;
    top: 10px;
    left: 20px;
    font-size: 1.5rem;
    animation: flicker 2s infinite alternate;
}

.kitchen-area::after {
    content: '🔥';
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 1.5rem;
    animation: flicker 2s infinite alternate-reverse;
}

.kitchen-area i {
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Restrooms */
.restrooms {
    grid-column: 3;
    grid-row: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background:
        linear-gradient(135deg, #34495E 0%, #5D6D7E 50%, #34495E 100%),
        repeating-linear-gradient(
            90deg,
            transparent,
            transparent 15px,
            rgba(255, 255, 255, 0.1) 15px,
            rgba(255, 255, 255, 0.1) 17px
        );
    color: white;
    border-radius: 15px;
    font-weight: 600;
    box-shadow:
        inset 0 0 20px rgba(255, 255, 255, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.3);
    border: 3px solid #2C3E50;
}

.restrooms i {
    font-size: 2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.restrooms::after {
    content: '🚻';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.2rem;
}

/* Flicker animation for kitchen */
@keyframes flicker {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.1); }
}

/* Decorative Elements */
.plant {
    position: absolute;
    font-size: 2rem;
    z-index: 5;
    animation: sway 3s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.plant-1 {
    top: 20px;
    left: 20px;
    animation-delay: 0s;
}

.plant-2 {
    top: 20px;
    right: 20px;
    animation-delay: 1s;
}

.plant-3 {
    bottom: 20px;
    left: 20px;
    animation-delay: 2s;
}

.plant-4 {
    bottom: 20px;
    right: 20px;
    animation-delay: 1.5s;
}

.chandelier {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 3rem;
    z-index: 10;
    animation: sparkle 2s ease-in-out infinite;
    filter: drop-shadow(0 4px 8px rgba(212, 175, 55, 0.5));
}

.host-station {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 15;
    border: 2px solid #fff;
}

.host-station i {
    font-size: 1rem;
}

/* Animations */
@keyframes sway {
    0%, 100% { transform: rotate(-2deg); }
    50% { transform: rotate(2deg); }
}

@keyframes sparkle {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        filter: drop-shadow(0 4px 8px rgba(212, 175, 55, 0.5));
    }
    50% {
        transform: translateX(-50%) scale(1.1);
        filter: drop-shadow(0 6px 12px rgba(212, 175, 55, 0.8));
    }
}

/* Table Styles */
.table {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 80px;
    border: 3px solid transparent;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.table::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 85%;
    height: 85%;
    background:
        /* Table surface with wood grain */
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.6) 0%, transparent 40%),
        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.3) 0%, transparent 30%),
        linear-gradient(135deg, #8B4513 0%, #A0522D 25%, #CD853F 50%, #A0522D 75%, #654321 100%),
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 2px,
            rgba(139, 69, 19, 0.1) 2px,
            rgba(139, 69, 19, 0.1) 4px
        );
    border-radius: inherit;
    z-index: -1;
    box-shadow:
        inset 0 3px 12px rgba(255, 255, 255, 0.4),
        inset 0 -3px 12px rgba(0, 0, 0, 0.4),
        0 0 0 2px rgba(139, 69, 19, 0.8);
}

/* Table settings overlay */
.table::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.9) 8%, transparent 12%),
        radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.9) 8%, transparent 12%),
        radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.9) 8%, transparent 12%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.9) 8%, transparent 12%),
        radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.3) 15%, transparent 20%);
    border-radius: inherit;
    z-index: 1;
    pointer-events: none;
    opacity: 0.8;
}

.table:hover {
    transform: translateY(-3px) scale(1.05);
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
}

.table-number {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--text-white);
    text-shadow:
        2px 2px 4px rgba(0, 0, 0, 0.8),
        0 0 10px rgba(212, 175, 55, 0.5);
    background: rgba(0, 0, 0, 0.6);
    padding: 4px 8px;
    border-radius: 15px;
    border: 2px solid var(--primary-color);
    backdrop-filter: blur(5px);
}

.seats {
    font-size: 0.8rem;
    color: var(--text-white);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    padding: 3px 8px;
    border-radius: 12px;
    margin-top: 4px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Table Types */
.round-table {
    border-radius: 50%;
    width: 90px;
    height: 90px;
    background:
        radial-gradient(circle at center, #8B4513 0%, #654321 70%, #3E2723 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
}

.round-table::before {
    border-radius: 50%;
}

.round-table::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
    height: 70%;
    border-radius: 50%;
    background:
        radial-gradient(circle at center, rgba(255, 255, 255, 0.1) 0%, transparent 70%),
        conic-gradient(from 0deg, #A0522D, #8B4513, #654321, #8B4513);
    z-index: -1;
}

.square-table {
    border-radius: 12px;
    width: 75px;
    height: 75px;
    background:
        linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%),
        linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%);
}

.square-table::before {
    border-radius: 12px;
}

.bar-table {
    border-radius: 20px;
    width: 100%;
    height: 60px;
    margin-bottom: 0.8rem;
    background:
        linear-gradient(135deg, #654321 0%, #8B4513 50%, #A0522D 100%),
        repeating-linear-gradient(90deg, transparent, transparent 10px, rgba(255, 255, 255, 0.1) 10px, rgba(255, 255, 255, 0.1) 12px);
}

.bar-table::before {
    border-radius: 20px;
}

.window-table {
    border-radius: 15px;
    width: 100%;
    height: 70px;
    margin-bottom: 0.8rem;
    background:
        linear-gradient(135deg, #8B4513 0%, #CD853F 50%, #A0522D 100%),
        radial-gradient(ellipse at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
}

.window-table::before {
    border-radius: 15px;
}

.window-table::after {
    content: '🪟';
    position: absolute;
    top: -15px;
    right: -10px;
    font-size: 1.2rem;
    z-index: 10;
}

.private-table {
    border-radius: 20px;
    width: 100%;
    height: 100px;
    background:
        linear-gradient(135deg, #2C1810 0%, #8B4513 50%, #654321 100%),
        radial-gradient(ellipse at center, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
}

.private-table::before {
    border-radius: 20px;
}

.private-table::after {
    content: '👑';
    position: absolute;
    top: -15px;
    right: -10px;
    font-size: 1.5rem;
    z-index: 10;
}

/* Chair Representations */
.table {
    --chair-size: 12px;
    --chair-color: #654321;
}

.round-table[data-seats="4"]::before,
.square-table[data-seats="2"]::before,
.window-table[data-seats="4"]::before {
    content: '';
    position: absolute;
    width: var(--chair-size);
    height: var(--chair-size);
    background: var(--chair-color);
    border-radius: 3px;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

/* 4-seat round table chairs */
.round-table[data-seats="4"] {
    --chair-distance: 50px;
}

.round-table[data-seats="4"]::before {
    top: calc(-1 * var(--chair-size) - 5px);
    left: 50%;
    transform: translateX(-50%);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        var(--chair-distance) var(--chair-distance) 0 0 var(--chair-color),
        calc(-1 * var(--chair-distance)) var(--chair-distance) 0 0 var(--chair-color),
        0 calc(var(--chair-distance) * 2 + 10px) 0 0 var(--chair-color);
}

/* 6-seat round table chairs */
.round-table[data-seats="6"] {
    position: relative;
}

.round-table[data-seats="6"]::before {
    content: '🪑🪑🪑🪑🪑🪑';
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    letter-spacing: 15px;
    z-index: 5;
}

/* 2-seat square table chairs */
.square-table[data-seats="2"]::before {
    top: calc(-1 * var(--chair-size) - 3px);
    left: 50%;
    transform: translateX(-50%);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 calc(var(--chair-size) * 4 + 6px) 0 0 var(--chair-color);
}

/* Bar table chairs */
.bar-table::before {
    content: '🪑🪑';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    letter-spacing: 20px;
    z-index: 5;
}

/* Window table chairs */
.window-table[data-seats="2"]::before {
    content: '🪑🪑';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    letter-spacing: 15px;
    z-index: 5;
}

.window-table[data-seats="4"]::before {
    content: '🪑🪑🪑🪑';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    letter-spacing: 8px;
    z-index: 5;
}

/* Private table chairs */
.private-table[data-seats="8"]::before {
    content: '🪑🪑🪑🪑🪑🪑🪑🪑';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 8px;
    letter-spacing: 5px;
    z-index: 5;
}

.private-table[data-seats="10"]::before {
    content: '🪑🪑🪑🪑🪑🪑🪑🪑🪑🪑';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 7px;
    letter-spacing: 3px;
    z-index: 5;
}

/* Table Status Colors */
.table.available {
    border: 3px solid #4CAF50;
    box-shadow:
        0 0 0 2px rgba(76, 175, 80, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.2);
}

.table.available .table-number {
    background: rgba(76, 175, 80, 0.8);
    border-color: #4CAF50;
}

.table.selected {
    border: 3px solid var(--primary-color);
    box-shadow:
        0 0 0 4px rgba(212, 175, 55, 0.4),
        0 0 20px rgba(212, 175, 55, 0.6),
        0 8px 25px rgba(0, 0, 0, 0.3);
    animation: pulse 2s infinite;
    transform: translateY(-5px) scale(1.1);
}

.table.selected .table-number {
    background: rgba(212, 175, 55, 0.9);
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.8);
}

.table.reserved {
    border: 3px solid #f44336;
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow:
        0 0 0 2px rgba(244, 67, 54, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.2);
}

.table.reserved .table-number {
    background: rgba(244, 67, 54, 0.8);
    border-color: #f44336;
}

.table.occupied {
    border: 3px solid #9E9E9E;
    cursor: not-allowed;
    opacity: 0.6;
    box-shadow:
        0 0 0 2px rgba(158, 158, 158, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.2);
}

.table.occupied .table-number {
    background: rgba(158, 158, 158, 0.8);
    border-color: #9E9E9E;
}

.table.reserved:hover,
.table.occupied:hover {
    transform: none;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

/* Selected Table Info */
.selected-table-info {
    background: var(--bg-light);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 2rem;
    animation: slideInUp 0.3s ease;
}

.selected-table-info h3 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.table-details {
    margin-bottom: 1.5rem;
}

.table-details p {
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.table-details strong {
    color: var(--text-dark);
}

/* Modal Styles */
.reservation-modal .modal-content {
    max-width: 600px;
    width: 90%;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-dark);
}

.modal-close {
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-dark);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Confirmation Modal */
.confirmation-modal .modal-header {
    text-align: center;
    border: none;
    padding-bottom: 0;
}

.confirmation-modal .modal-header i {
    color: #4CAF50;
    font-size: 3rem;
    margin-bottom: 1rem;
}

.confirmation-details {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
}

.confirmation-details h4 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-row .label {
    font-weight: 600;
    color: var(--text-dark);
}

.detail-row .value {
    color: var(--text-light);
}

.confirmation-message {
    text-align: center;
    color: var(--text-light);
    font-style: italic;
}

/* Tooltip */
.table-tooltip {
    position: absolute;
    background: var(--bg-dark);
    color: var(--text-white);
    padding: 0.8rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    max-width: 200px;
}

.table-tooltip.show {
    opacity: 1;
}

.tooltip-content h4 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 0.9rem;
}

.tooltip-content p {
    margin: 0;
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Animations */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(212, 175, 55, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(212, 175, 55, 0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== RESPONSIVE DESIGN FOR TABLE RESERVATION ===== */

/* Tablets */
@media screen and (max-width: 768px) {
    .reservation-header {
        padding: 100px 0 40px;
    }

    .reservation-header h1 {
        font-size: 2.5rem;
    }

    .legend-items {
        gap: 1rem;
    }

    .legend-item {
        font-size: 0.9rem;
    }

    .restaurant-floor {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto auto auto auto;
        gap: 1rem;
        padding: 1.5rem;
        min-height: auto;
    }

    .entrance {
        grid-column: 1;
        grid-row: 1;
    }

    .bar-area {
        grid-column: 1;
        grid-row: 2;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .main-dining {
        grid-column: 1;
        grid-row: 3;
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
    }

    .window-side {
        grid-column: 1;
        grid-row: 4;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-around;
    }

    .private-dining {
        grid-column: 1;
        grid-row: 5;
        flex-direction: column;
        align-items: center;
    }

    .kitchen-area {
        grid-column: 1;
        grid-row: 6;
    }

    .restrooms {
        grid-column: 1;
        grid-row: 7;
    }

    .table {
        min-height: 50px;
    }

    .round-table {
        width: 60px;
        height: 60px;
    }

    .square-table {
        width: 50px;
        height: 50px;
    }

    .bar-table,
    .window-table {
        width: 80px;
        height: 45px;
        margin: 0.25rem;
    }

    .private-table {
        width: 120px;
        height: 60px;
        margin: 0.5rem 0;
    }

    .selected-table-info {
        padding: 1rem;
    }

    .reservation-modal .modal-content {
        width: 95%;
        margin: 5% auto;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Mobile Phones */
@media screen and (max-width: 480px) {
    .reservation-header {
        padding: 90px 0 30px;
    }

    .reservation-header h1 {
        font-size: 2rem;
    }

    .reservation-header p {
        font-size: 1rem;
    }

    .legend-section {
        padding: 1.5rem 0;
    }

    .legend-items {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .legend-item {
        font-size: 0.85rem;
    }

    .restaurant-floor {
        padding: 1rem;
        gap: 0.8rem;
    }

    .area-label {
        font-size: 0.7rem;
        padding: 2px 8px;
    }

    .entrance,
    .kitchen-area,
    .restrooms {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .entrance i,
    .kitchen-area i,
    .restrooms i {
        font-size: 1.2rem;
    }

    .bar-area,
    .window-side,
    .private-dining {
        padding: 0.8rem;
    }

    .main-dining {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(6, 1fr);
        gap: 0.5rem;
        padding: 0.8rem;
    }

    .table {
        min-height: 40px;
    }

    .table-number {
        font-size: 0.8rem;
    }

    .seats {
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    .round-table {
        width: 45px;
        height: 45px;
    }

    .square-table {
        width: 40px;
        height: 40px;
    }

    .bar-table,
    .window-table {
        width: 60px;
        height: 35px;
        margin: 0.2rem;
    }

    .private-table {
        width: 100px;
        height: 50px;
        margin: 0.3rem 0;
    }

    .selected-table-info {
        padding: 0.8rem;
        margin-top: 1rem;
    }

    .selected-table-info h3 {
        font-size: 1.1rem;
    }

    .table-details p {
        font-size: 0.9rem;
    }

    .reservation-modal .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 0.9rem;
    }

    .confirmation-modal .modal-content {
        width: 95%;
        margin: 15% auto;
    }

    .confirmation-modal .modal-header i {
        font-size: 2.5rem;
    }

    .confirmation-details {
        padding: 1rem;
    }

    .detail-row {
        font-size: 0.9rem;
    }

    .table-tooltip {
        max-width: 150px;
        padding: 0.6rem;
    }

    .tooltip-content h4 {
        font-size: 0.8rem;
    }

    .tooltip-content p {
        font-size: 0.7rem;
    }
}

/* Extra Small Devices */
@media screen and (max-width: 320px) {
    .reservation-header h1 {
        font-size: 1.8rem;
    }

    .restaurant-floor {
        padding: 0.8rem;
    }

    .main-dining {
        grid-template-columns: 1fr;
        grid-template-rows: repeat(11, 1fr);
    }

    .round-table {
        width: 40px;
        height: 40px;
    }

    .square-table {
        width: 35px;
        height: 35px;
    }

    .bar-table,
    .window-table {
        width: 50px;
        height: 30px;
    }

    .private-table {
        width: 80px;
        height: 40px;
    }

    .table-number {
        font-size: 0.7rem;
    }

    .seats {
        font-size: 0.5rem;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .table {
        min-height: 50px;
        min-width: 50px;
    }

    .table:hover {
        transform: none;
    }

    .table:active {
        transform: scale(0.95);
    }

    .round-table {
        width: 55px;
        height: 55px;
    }

    .square-table {
        width: 50px;
        height: 50px;
    }

    .bar-table,
    .window-table {
        height: 50px;
    }

    .table-tooltip {
        display: none;
    }
}

/* Landscape Orientation for Mobile */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .reservation-header {
        padding: 80px 0 20px;
    }

    .restaurant-floor {
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: auto 1fr auto;
        min-height: 400px;
    }

    .entrance {
        grid-column: 2;
        grid-row: 1;
    }

    .bar-area {
        grid-column: 1;
        grid-row: 2;
    }

    .main-dining {
        grid-column: 2;
        grid-row: 2;
    }

    .window-side {
        grid-column: 3;
        grid-row: 2;
    }

    .private-dining {
        grid-column: 1;
        grid-row: 3;
    }

    .kitchen-area {
        grid-column: 2;
        grid-row: 3;
    }

    .restrooms {
        grid-column: 3;
        grid-row: 3;
    }
}
