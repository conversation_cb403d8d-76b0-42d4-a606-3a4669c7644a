<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Taste Haven - Exquisite Dining Experience</title>
    <meta name="description" content="Experience culinary excellence at Taste Haven - where every meal is a journey of flavors">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>Taste Haven</h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link">Home</a>
                <a href="#menu" class="nav-link">Menu</a>
                <a href="#reservation" class="nav-link">Reservations</a>
                <a href="table-reservation.html" class="nav-link">Table Selection</a>
                <a href="#gallery" class="nav-link">Gallery</a>
                <a href="#contact" class="nav-link">Contact</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">Welcome to Taste Haven</h1>
            <p class="hero-subtitle">Where culinary artistry meets exceptional dining</p>
            <div class="hero-buttons">
                <a href="table-reservation.html" class="btn btn-primary">Select Your Table</a>
                <a href="#reservation" class="btn btn-secondary">Quick Reservation</a>
                <a href="#menu" class="btn btn-secondary">View Menu</a>
            </div>
        </div>
        <div class="scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- Menu Section -->
    <section id="menu" class="menu-section">
        <div class="container">
            <div class="section-header">
                <h2>Our Exquisite Menu</h2>
                <p>Discover our carefully crafted dishes made with the finest ingredients</p>
            </div>
            
            <div class="menu-filters">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="appetizers">Appetizers</button>
                <button class="filter-btn" data-filter="mains">Main Dishes</button>
                <button class="filter-btn" data-filter="desserts">Desserts</button>
                <button class="filter-btn" data-filter="beverages">Beverages</button>
            </div>
            
            <div class="menu-grid" id="menu-grid">
                <!-- Menu items will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Reservation Section -->
    <section id="reservation" class="reservation-section">
        <div class="container">
            <div class="section-header">
                <h2>Make a Reservation</h2>
                <p>Book your table for an unforgettable dining experience</p>
            </div>
            
            <div class="reservation-content">
                <form class="reservation-form" id="reservation-form">
                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" name="name" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="date">Date</label>
                            <input type="date" id="date" name="date" required>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="time">Time</label>
                            <select id="time" name="time" required>
                                <option value="">Select Time</option>
                                <option value="17:00">5:00 PM</option>
                                <option value="17:30">5:30 PM</option>
                                <option value="18:00">6:00 PM</option>
                                <option value="18:30">6:30 PM</option>
                                <option value="19:00">7:00 PM</option>
                                <option value="19:30">7:30 PM</option>
                                <option value="20:00">8:00 PM</option>
                                <option value="20:30">8:30 PM</option>
                                <option value="21:00">9:00 PM</option>
                            </select>
                            <span class="error-message"></span>
                        </div>
                        
                        <div class="form-group">
                            <label for="guests">Guests</label>
                            <select id="guests" name="guests" required>
                                <option value="">Select</option>
                                <option value="1">1 Guest</option>
                                <option value="2">2 Guests</option>
                                <option value="3">3 Guests</option>
                                <option value="4">4 Guests</option>
                                <option value="5">5 Guests</option>
                                <option value="6">6 Guests</option>
                                <option value="7">7 Guests</option>
                                <option value="8">8 Guests</option>
                            </select>
                            <span class="error-message"></span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="special-requests">Special Requests (Optional)</label>
                        <textarea id="special-requests" name="special-requests" rows="4" placeholder="Any dietary restrictions or special occasions?"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Make Reservation</button>
                </form>
                
                <div class="reservation-info">
                    <h3>Reservation Information</h3>
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h4>Opening Hours</h4>
                            <p>Monday - Sunday: 5:00 PM - 11:00 PM</p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>Call Us</h4>
                            <p>+****************</p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-info-circle"></i>
                        <div>
                            <h4>Cancellation Policy</h4>
                            <p>Please cancel at least 2 hours in advance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery-section">
        <div class="container">
            <div class="section-header">
                <h2>Gallery</h2>
                <p>Take a glimpse into our restaurant's atmosphere and culinary creations</p>
            </div>
            
            <div class="gallery-grid" id="gallery-grid">
                <!-- Gallery images will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer id="contact" class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Taste Haven</h3>
                    <p>Experience culinary excellence in every bite. We're committed to providing you with an unforgettable dining experience.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <p>123 Gourmet Street<br>Culinary District, CD 12345</p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <p>+****************</p>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <p><EMAIL></p>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Opening Hours</h4>
                    <div class="hours-item">
                        <span>Monday - Thursday</span>
                        <span>5:00 PM - 10:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span>Friday - Saturday</span>
                        <span>5:00 PM - 11:00 PM</span>
                    </div>
                    <div class="hours-item">
                        <span>Sunday</span>
                        <span>4:00 PM - 9:00 PM</span>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Location</h4>
                    <div class="map-container">
                        <iframe 
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.123456789!2d-74.0059413!3d40.7127753!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjAiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v1234567890123"
                            width="100%" 
                            height="200" 
                            style="border:0;" 
                            allowfullscreen="" 
                            loading="lazy" 
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Taste Haven. All rights reserved. | Designed with ❤️ for food lovers</p>
            </div>
        </div>
    </footer>

    <!-- Confirmation Modal -->
    <div class="modal" id="confirmation-modal">
        <div class="modal-content">
            <div class="modal-header">
                <i class="fas fa-check-circle"></i>
                <h3>Reservation Confirmed!</h3>
            </div>
            <div class="modal-body">
                <p>Thank you for choosing Taste Haven. Your reservation has been successfully submitted.</p>
                <p>We'll send you a confirmation email shortly with all the details.</p>
            </div>
            <button class="btn btn-primary" onclick="closeModal()">Close</button>
        </div>
    </div>

    <!-- Lightbox for Gallery -->
    <div class="lightbox" id="lightbox">
        <div class="lightbox-content">
            <span class="lightbox-close">&times;</span>
            <img class="lightbox-image" id="lightbox-image" src="" alt="">
            <div class="lightbox-nav">
                <button class="lightbox-prev" id="lightbox-prev">&#10094;</button>
                <button class="lightbox-next" id="lightbox-next">&#10095;</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/menu-data.js"></script>
    <script src="js/gallery-data.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
