// Gallery Data for Taste Haven Restaurant
const galleryData = [
    {
        id: 1,
        src: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Restaurant interior with elegant dining setup",
        category: "interior"
    },
    {
        id: 2,
        src: "https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Beautifully plated gourmet dish",
        category: "food"
    },
    {
        id: 3,
        src: "https://images.unsplash.com/photo-1559847844-d721426d6edc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Fresh seafood preparation",
        category: "food"
    },
    {
        id: 4,
        src: "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Modern restaurant bar area",
        category: "interior"
    },
    {
        id: 5,
        src: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Premium steak dish presentation",
        category: "food"
    },
    {
        id: 6,
        src: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Grilled salmon with vegetables",
        category: "food"
    },
    {
        id: 7,
        src: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Cozy restaurant seating area",
        category: "interior"
    },
    {
        id: 8,
        src: "https://images.unsplash.com/photo-1432139555190-58524dae6a55?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Artisanal pasta dish",
        category: "food"
    },
    {
        id: 9,
        src: "https://images.unsplash.com/photo-1563379091339-03246963d96c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Seafood risotto presentation",
        category: "food"
    },
    {
        id: 10,
        src: "https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Herb-crusted lamb dish",
        category: "food"
    },
    {
        id: 11,
        src: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Vegetarian gourmet dish",
        category: "food"
    },
    {
        id: 12,
        src: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Decadent chocolate dessert",
        category: "desserts"
    },
    {
        id: 13,
        src: "https://images.unsplash.com/photo-1470324161839-ce2bb6fa6bc3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Classic crème brûlée",
        category: "desserts"
    },
    {
        id: 14,
        src: "https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Traditional tiramisu",
        category: "desserts"
    },
    {
        id: 15,
        src: "https://images.unsplash.com/photo-1565958011703-44f9829ba187?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Fresh lemon tart",
        category: "desserts"
    },
    {
        id: 16,
        src: "https://images.unsplash.com/photo-1514362545857-3bc16c4c7d1b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Signature cocktail presentation",
        category: "beverages"
    },
    {
        id: 17,
        src: "https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Wine selection display",
        category: "beverages"
    },
    {
        id: 18,
        src: "https://images.unsplash.com/photo-1608270586620-248524c67de9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Craft beer selection",
        category: "beverages"
    },
    {
        id: 19,
        src: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Artisan coffee preparation",
        category: "beverages"
    },
    {
        id: 20,
        src: "https://images.unsplash.com/photo-1600891964092-4316c288032e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Chef preparing dishes in kitchen",
        category: "kitchen"
    },
    {
        id: 21,
        src: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Professional kitchen setup",
        category: "kitchen"
    },
    {
        id: 22,
        src: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Restaurant outdoor terrace",
        category: "exterior"
    },
    {
        id: 23,
        src: "https://images.unsplash.com/photo-1552566626-52f8b828add9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Evening restaurant ambiance",
        category: "interior"
    },
    {
        id: 24,
        src: "https://images.unsplash.com/photo-1590846406792-0adc7f938f1d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
        alt: "Special event dining setup",
        category: "events"
    }
];

// Function to get gallery images by category
function getGalleryByCategory(category) {
    if (category === 'all') {
        return galleryData;
    }
    return galleryData.filter(item => item.category === category);
}

// Function to get a single gallery item by ID
function getGalleryItemById(id) {
    return galleryData.find(item => item.id === id);
}

// Function to get random gallery items
function getRandomGalleryItems(count = 12) {
    const shuffled = [...galleryData].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
}
