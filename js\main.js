// Main JavaScript for Taste Haven Restaurant Website

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initHeroSection();
    initMenuSection();
    initReservationForm();
    initGallery();
    initScrollAnimations();
    initSmoothScrolling();
});

// ===== NAVIGATION FUNCTIONALITY =====
function initNavigation() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Mobile menu toggle
    navToggle.addEventListener('click', function() {
        navToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            navToggle.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Active link highlighting
    window.addEventListener('scroll', function() {
        let current = '';
        const sections = document.querySelectorAll('section[id]');
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
}

// ===== HERO SECTION FUNCTIONALITY =====
function initHeroSection() {
    const scrollIndicator = document.querySelector('.scroll-indicator');
    
    // Scroll indicator click
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            document.getElementById('menu').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }

    // Hero animations on load
    const heroContent = document.querySelector('.hero-content');
    if (heroContent) {
        setTimeout(() => {
            heroContent.style.opacity = '1';
            heroContent.style.transform = 'translateY(0)';
        }, 500);
    }
}

// ===== MENU SECTION FUNCTIONALITY =====
function initMenuSection() {
    const menuGrid = document.getElementById('menu-grid');
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    // Render initial menu
    renderMenu('all');
    
    // Filter button functionality
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Get filter category
            const category = this.getAttribute('data-filter');
            
            // Render filtered menu
            renderMenu(category);
        });
    });
}

function renderMenu(category) {
    const menuGrid = document.getElementById('menu-grid');
    const menuItems = getMenuByCategory(category);
    
    // Clear existing items
    menuGrid.innerHTML = '';
    
    // Create menu items
    menuItems.forEach((item, index) => {
        const menuItemElement = createMenuItemElement(item);
        menuGrid.appendChild(menuItemElement);
        
        // Add staggered animation
        setTimeout(() => {
            menuItemElement.classList.add('fade-in');
        }, index * 100);
    });
}

function createMenuItemElement(item) {
    const menuItem = document.createElement('div');
    menuItem.className = 'menu-item';
    menuItem.setAttribute('data-category', item.category);
    
    menuItem.innerHTML = `
        <img src="${item.image}" alt="${item.name}" class="menu-item-image" onclick="zoomImage('${item.image}', '${item.name}')">
        <div class="menu-item-content">
            <div class="menu-item-header">
                <h3 class="menu-item-title">${item.name}</h3>
                <span class="menu-item-price">${item.price}</span>
            </div>
            <p class="menu-item-description">${item.description}</p>
            <span class="menu-item-category">${item.category.charAt(0).toUpperCase() + item.category.slice(1)}</span>
        </div>
    `;
    
    return menuItem;
}

// Image zoom functionality
function zoomImage(src, alt) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    
    lightboxImage.src = src;
    lightboxImage.alt = alt;
    lightbox.style.display = 'block';
    
    // Close lightbox when clicking outside image
    lightbox.addEventListener('click', function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    });
}

// ===== RESERVATION FORM FUNCTIONALITY =====
function initReservationForm() {
    const form = document.getElementById('reservation-form');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    // Set minimum date to today
    const dateInput = document.getElementById('date');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            // Show success modal
            showConfirmationModal();
            // Reset form
            form.reset();
            // Clear any error states
            clearFormErrors();
        }
    });
    
    // Real-time validation
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });
}

function validateForm() {
    const form = document.getElementById('reservation-form');
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const formGroup = field.closest('.form-group');
    const errorMessage = formGroup.querySelector('.error-message');
    let isValid = true;
    let message = '';
    
    // Remove previous error state
    formGroup.classList.remove('error');
    errorMessage.classList.remove('show');
    
    // Check if field is empty
    if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        message = 'This field is required';
    }
    // Email validation
    else if (field.type === 'email' && field.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }
    }
    // Phone validation
    else if (field.type === 'tel' && field.value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(field.value.replace(/[\s\-\(\)]/g, ''))) {
            isValid = false;
            message = 'Please enter a valid phone number';
        }
    }
    // Date validation
    else if (field.type === 'date' && field.value) {
        const selectedDate = new Date(field.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate < today) {
            isValid = false;
            message = 'Please select a future date';
        }
    }
    
    if (!isValid) {
        formGroup.classList.add('error');
        errorMessage.textContent = message;
        errorMessage.classList.add('show');
    }
    
    return isValid;
}

function clearFormErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const formGroups = document.querySelectorAll('.form-group');
    
    errorMessages.forEach(msg => {
        msg.classList.remove('show');
    });
    
    formGroups.forEach(group => {
        group.classList.remove('error');
    });
}

function showConfirmationModal() {
    const modal = document.getElementById('confirmation-modal');
    modal.style.display = 'block';
    
    // Auto close after 5 seconds
    setTimeout(() => {
        closeModal();
    }, 5000);
}

function closeModal() {
    const modal = document.getElementById('confirmation-modal');
    modal.style.display = 'none';
}

// ===== GALLERY FUNCTIONALITY =====
function initGallery() {
    const galleryGrid = document.getElementById('gallery-grid');
    const galleryImages = getRandomGalleryItems(12);
    
    // Render gallery
    galleryImages.forEach((item, index) => {
        const galleryItem = document.createElement('div');
        galleryItem.className = 'gallery-item';
        galleryItem.innerHTML = `<img src="${item.src}" alt="${item.alt}" onclick="openLightbox(${index})">`;
        galleryGrid.appendChild(galleryItem);
    });
    
    // Store gallery data globally for lightbox navigation
    window.currentGallery = galleryImages;
    window.currentImageIndex = 0;
}

function openLightbox(index) {
    const lightbox = document.getElementById('lightbox');
    const lightboxImage = document.getElementById('lightbox-image');
    
    window.currentImageIndex = index;
    const currentImage = window.currentGallery[index];
    
    lightboxImage.src = currentImage.src;
    lightboxImage.alt = currentImage.alt;
    lightbox.style.display = 'block';
    
    // Setup navigation
    setupLightboxNavigation();
}

function setupLightboxNavigation() {
    const lightbox = document.getElementById('lightbox');
    const lightboxClose = document.querySelector('.lightbox-close');
    const lightboxPrev = document.getElementById('lightbox-prev');
    const lightboxNext = document.getElementById('lightbox-next');
    
    // Close lightbox
    lightboxClose.onclick = closeLightbox;
    lightbox.onclick = function(e) {
        if (e.target === lightbox) {
            closeLightbox();
        }
    };
    
    // Navigation
    lightboxPrev.onclick = function() {
        window.currentImageIndex = (window.currentImageIndex - 1 + window.currentGallery.length) % window.currentGallery.length;
        updateLightboxImage();
    };
    
    lightboxNext.onclick = function() {
        window.currentImageIndex = (window.currentImageIndex + 1) % window.currentGallery.length;
        updateLightboxImage();
    };
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (lightbox.style.display === 'block') {
            if (e.key === 'Escape') {
                closeLightbox();
            } else if (e.key === 'ArrowLeft') {
                lightboxPrev.click();
            } else if (e.key === 'ArrowRight') {
                lightboxNext.click();
            }
        }
    });
}

function updateLightboxImage() {
    const lightboxImage = document.getElementById('lightbox-image');
    const currentImage = window.currentGallery[window.currentImageIndex];
    
    lightboxImage.src = currentImage.src;
    lightboxImage.alt = currentImage.alt;
}

function closeLightbox() {
    const lightbox = document.getElementById('lightbox');
    lightbox.style.display = 'none';
}

// ===== SCROLL ANIMATIONS =====
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.section-header, .menu-item, .reservation-content, .gallery-item, .footer-section');
    animateElements.forEach(el => {
        observer.observe(el);
    });
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}
