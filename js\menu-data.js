// Menu Data for Taste Haven Restaurant
const menuData = [
    // Appetizers
    {
        id: 1,
        name: "Truffle Arancini",
        description: "Crispy risotto balls filled with wild mushrooms and truffle oil, served with parmesan aioli",
        price: "$16",
        category: "appetizers",
        image: "https://images.unsplash.com/photo-1551218808-94e220e084d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 2,
        name: "Seared Scallops",
        description: "Pan-seared scallops with cauliflower puree, pancetta crisps, and microgreens",
        price: "$22",
        category: "appetizers",
        image: "https://images.unsplash.com/photo-1559847844-d721426d6edc?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 3,
        name: "Burrata Caprese",
        description: "Fresh burrata cheese with heirloom tomatoes, basil oil, and aged balsamic reduction",
        price: "$18",
        category: "appetizers",
        image: "https://images.unsplash.com/photo-1608897013039-887f21d8c804?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 4,
        name: "Tuna Tartare",
        description: "Yellowfin tuna with avocado, cucumber, sesame oil, and crispy wonton chips",
        price: "$20",
        category: "appetizers",
        image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },

    // Main Dishes
    {
        id: 5,
        name: "Wagyu Beef Tenderloin",
        description: "8oz premium wagyu beef with roasted vegetables, red wine jus, and truffle mashed potatoes",
        price: "$65",
        category: "mains",
        image: "https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 6,
        name: "Pan-Seared Salmon",
        description: "Atlantic salmon with quinoa pilaf, roasted asparagus, and lemon herb butter",
        price: "$32",
        category: "mains",
        image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 7,
        name: "Duck Confit",
        description: "Slow-cooked duck leg with cherry gastrique, wild rice, and seasonal vegetables",
        price: "$38",
        category: "mains",
        image: "https://images.unsplash.com/photo-1432139555190-58524dae6a55?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 8,
        name: "Lobster Risotto",
        description: "Creamy arborio rice with fresh lobster, saffron, and microgreens",
        price: "$42",
        category: "mains",
        image: "https://images.unsplash.com/photo-1563379091339-03246963d96c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 9,
        name: "Rack of Lamb",
        description: "Herb-crusted lamb with ratatouille, rosemary jus, and garlic confit",
        price: "$45",
        category: "mains",
        image: "https://images.unsplash.com/photo-1544025162-d76694265947?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 10,
        name: "Vegetarian Wellington",
        description: "Puff pastry filled with roasted vegetables, mushroom duxelles, and herb sauce",
        price: "$28",
        category: "mains",
        image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },

    // Desserts
    {
        id: 11,
        name: "Chocolate Lava Cake",
        description: "Warm chocolate cake with molten center, vanilla ice cream, and berry compote",
        price: "$14",
        category: "desserts",
        image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 12,
        name: "Crème Brûlée",
        description: "Classic vanilla custard with caramelized sugar crust and fresh berries",
        price: "$12",
        category: "desserts",
        image: "https://images.unsplash.com/photo-1470324161839-ce2bb6fa6bc3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 13,
        name: "Tiramisu",
        description: "Traditional Italian dessert with espresso-soaked ladyfingers and mascarpone",
        price: "$13",
        category: "desserts",
        image: "https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 14,
        name: "Lemon Tart",
        description: "Buttery pastry shell filled with tangy lemon curd and meringue",
        price: "$11",
        category: "desserts",
        image: "https://images.unsplash.com/photo-1565958011703-44f9829ba187?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },

    // Beverages
    {
        id: 15,
        name: "Signature Cocktail",
        description: "House special with premium spirits, fresh herbs, and seasonal fruits",
        price: "$16",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1514362545857-3bc16c4c7d1b?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 16,
        name: "Wine Selection",
        description: "Curated selection of fine wines from renowned vineyards worldwide",
        price: "$12-45",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1510812431401-41d2bd2722f3?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 17,
        name: "Craft Beer",
        description: "Local and imported craft beers, rotating selection of seasonal brews",
        price: "$8-12",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1608270586620-248524c67de9?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 18,
        name: "Artisan Coffee",
        description: "Single-origin coffee beans, expertly roasted and brewed to perfection",
        price: "$6",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 19,
        name: "Fresh Juices",
        description: "Freshly squeezed seasonal fruit and vegetable juices",
        price: "$7",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1622597467836-f3285f2131b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    },
    {
        id: 20,
        name: "Sparkling Water",
        description: "Premium sparkling water with fresh citrus and herbs",
        price: "$5",
        category: "beverages",
        image: "https://images.unsplash.com/photo-1544145945-f90425340c7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
    }
];

// Function to get menu items by category
function getMenuByCategory(category) {
    if (category === 'all') {
        return menuData;
    }
    return menuData.filter(item => item.category === category);
}

// Function to get a single menu item by ID
function getMenuItemById(id) {
    return menuData.find(item => item.id === id);
}
