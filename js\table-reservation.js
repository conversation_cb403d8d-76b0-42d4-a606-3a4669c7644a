// Table Reservation System JavaScript

// Global variables
let selectedTable = null;
let reservations = JSON.parse(localStorage.getItem('tableReservations')) || {};

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    initTableReservationSystem();
    loadExistingReservations();
    initTooltips();
    initFormValidation();
});

// ===== INITIALIZATION =====
function initTableReservationSystem() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // Add click event listeners
        table.addEventListener('click', handleTableClick);
        
        // Add hover events for tooltips
        table.addEventListener('mouseenter', showTooltip);
        table.addEventListener('mouseleave', hideTooltip);
        table.addEventListener('mousemove', updateTooltipPosition);
    });
    
    // Set minimum date for reservation
    const dateInput = document.getElementById('reservation-date');
    const today = new Date().toISOString().split('T')[0];
    dateInput.min = today;
    
    // Initialize navigation
    initNavigation();
}

function loadExistingReservations() {
    // Apply existing reservations from localStorage
    Object.keys(reservations).forEach(tableId => {
        const table = document.querySelector(`[data-table="${tableId}"]`);
        if (table && reservations[tableId].status === 'reserved') {
            table.classList.remove('available');
            table.classList.add('reserved');
        }
    });
}

// ===== TABLE SELECTION LOGIC =====
function handleTableClick(event) {
    const table = event.currentTarget;
    const tableId = table.getAttribute('data-table');
    
    // Check if table is available
    if (table.classList.contains('reserved') || table.classList.contains('occupied')) {
        showUnavailableMessage(table);
        return;
    }
    
    // Clear previous selection
    clearSelection();
    
    // Select new table
    table.classList.add('selected');
    selectedTable = {
        id: tableId,
        number: tableId,
        seats: table.getAttribute('data-seats'),
        type: table.getAttribute('data-type'),
        special: table.getAttribute('data-special')
    };
    
    // Show table information
    showSelectedTableInfo();
    
    // Update party size options
    updatePartySizeOptions();
}

function clearSelection() {
    // Remove selection from all tables
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        table.classList.remove('selected');
    });
    
    // Hide table info
    const tableInfo = document.getElementById('selected-table-info');
    tableInfo.style.display = 'none';
    
    selectedTable = null;
}

function showSelectedTableInfo() {
    const tableInfo = document.getElementById('selected-table-info');
    const tableNumber = document.getElementById('selected-table-number');
    const tableSeats = document.getElementById('selected-table-seats');
    const tableType = document.getElementById('selected-table-type');
    const tableSpecial = document.getElementById('selected-table-special');
    const tableSpecialText = document.getElementById('selected-table-special-text');
    
    tableNumber.textContent = selectedTable.number;
    tableSeats.textContent = selectedTable.seats;
    tableType.textContent = selectedTable.type;
    
    if (selectedTable.special) {
        tableSpecial.style.display = 'block';
        tableSpecialText.textContent = selectedTable.special;
    } else {
        tableSpecial.style.display = 'none';
    }
    
    tableInfo.style.display = 'block';
    tableInfo.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

function showUnavailableMessage(table) {
    const status = table.classList.contains('reserved') ? 'reserved' : 'occupied';
    const message = status === 'reserved' 
        ? 'This table is already reserved. Please select another table.'
        : 'This table is currently occupied. Please select another table.';
    
    // Create temporary message
    const messageDiv = document.createElement('div');
    messageDiv.className = 'unavailable-message';
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #f44336;
        color: white;
        padding: 1rem 2rem;
        border-radius: 8px;
        z-index: 2000;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        animation: fadeInOut 3s ease;
    `;
    
    document.body.appendChild(messageDiv);
    
    // Remove message after 3 seconds
    setTimeout(() => {
        document.body.removeChild(messageDiv);
    }, 3000);
    
    // Add CSS animation
    if (!document.getElementById('unavailable-animation')) {
        const style = document.createElement('style');
        style.id = 'unavailable-animation';
        style.textContent = `
            @keyframes fadeInOut {
                0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20%, 80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            }
        `;
        document.head.appendChild(style);
    }
}

function updatePartySizeOptions() {
    const partySizeSelect = document.getElementById('party-size');
    const maxSeats = parseInt(selectedTable.seats);
    
    // Clear existing options
    partySizeSelect.innerHTML = '<option value="">Select Party Size</option>';
    
    // Add options up to table capacity
    for (let i = 1; i <= maxSeats; i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = `${i} ${i === 1 ? 'Person' : 'People'}`;
        partySizeSelect.appendChild(option);
    }
}

// ===== TOOLTIP FUNCTIONALITY =====
function initTooltips() {
    // Create tooltip element if it doesn't exist
    if (!document.getElementById('table-tooltip')) {
        const tooltip = document.createElement('div');
        tooltip.id = 'table-tooltip';
        tooltip.className = 'table-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-content">
                <h4 id="tooltip-table"></h4>
                <p id="tooltip-info"></p>
            </div>
        `;
        document.body.appendChild(tooltip);
    }
}

function showTooltip(event) {
    const table = event.currentTarget;
    const tooltip = document.getElementById('table-tooltip');
    const tooltipTable = document.getElementById('tooltip-table');
    const tooltipInfo = document.getElementById('tooltip-info');
    
    const tableId = table.getAttribute('data-table');
    const seats = table.getAttribute('data-seats');
    const type = table.getAttribute('data-type');
    const special = table.getAttribute('data-special');
    
    let status = 'Available';
    if (table.classList.contains('reserved')) status = 'Reserved';
    if (table.classList.contains('occupied')) status = 'Occupied';
    if (table.classList.contains('selected')) status = 'Selected';
    
    tooltipTable.textContent = `Table ${tableId}`;
    
    let info = `${type} • ${seats} seats • ${status}`;
    if (special) {
        info += ` • ${special}`;
    }
    
    tooltipInfo.textContent = info;
    
    tooltip.classList.add('show');
}

function hideTooltip() {
    const tooltip = document.getElementById('table-tooltip');
    tooltip.classList.remove('show');
}

function updateTooltipPosition(event) {
    const tooltip = document.getElementById('table-tooltip');
    const x = event.clientX + 10;
    const y = event.clientY - 10;
    
    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
}

// ===== MODAL FUNCTIONALITY =====
function openReservationModal() {
    if (!selectedTable) return;
    
    const modal = document.getElementById('reservation-modal');
    const modalTableNumber = document.getElementById('modal-table-number');
    
    modalTableNumber.textContent = selectedTable.number;
    modal.style.display = 'block';
    
    // Focus on first input
    setTimeout(() => {
        document.getElementById('customer-name').focus();
    }, 100);
}

function closeReservationModal() {
    const modal = document.getElementById('reservation-modal');
    modal.style.display = 'none';
    
    // Clear form
    const form = document.getElementById('table-reservation-form');
    form.reset();
    clearFormErrors();
}

function closeConfirmationModal() {
    const modal = document.getElementById('confirmation-modal');
    modal.style.display = 'none';
    
    // Clear selection and close reservation modal
    clearSelection();
    closeReservationModal();
}

// ===== FORM VALIDATION =====
function initFormValidation() {
    const form = document.getElementById('table-reservation-form');
    const inputs = form.querySelectorAll('input, select, textarea');
    
    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (validateReservationForm()) {
            processReservation();
        }
    });
    
    // Real-time validation
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });
}

function validateReservationForm() {
    const form = document.getElementById('table-reservation-form');
    const inputs = form.querySelectorAll('input[required], select[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const formGroup = field.closest('.form-group');
    const errorMessage = formGroup.querySelector('.error-message');
    let isValid = true;
    let message = '';
    
    // Remove previous error state
    formGroup.classList.remove('error');
    errorMessage.classList.remove('show');
    
    // Check if field is empty
    if (field.hasAttribute('required') && !field.value.trim()) {
        isValid = false;
        message = 'This field is required';
    }
    // Email validation
    else if (field.type === 'email' && field.value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(field.value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }
    }
    // Phone validation
    else if (field.type === 'tel' && field.value) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(field.value.replace(/[\s\-\(\)]/g, ''))) {
            isValid = false;
            message = 'Please enter a valid phone number';
        }
    }
    // Date validation
    else if (field.type === 'date' && field.value) {
        const selectedDate = new Date(field.value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        if (selectedDate < today) {
            isValid = false;
            message = 'Please select a future date';
        }
    }
    
    if (!isValid) {
        formGroup.classList.add('error');
        errorMessage.textContent = message;
        errorMessage.classList.add('show');
    }
    
    return isValid;
}

function clearFormErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const formGroups = document.querySelectorAll('.form-group');
    
    errorMessages.forEach(msg => {
        msg.classList.remove('show');
    });
    
    formGroups.forEach(group => {
        group.classList.remove('error');
    });
}

// ===== RESERVATION PROCESSING =====
function processReservation() {
    const form = document.getElementById('table-reservation-form');
    const formData = new FormData(form);
    
    const reservation = {
        tableId: selectedTable.id,
        tableNumber: selectedTable.number,
        name: formData.get('name'),
        phone: formData.get('phone'),
        email: formData.get('email'),
        date: formData.get('date'),
        time: formData.get('time'),
        partySize: formData.get('party-size'),
        specialRequests: formData.get('special-requests'),
        status: 'reserved',
        timestamp: new Date().toISOString()
    };
    
    // Save to localStorage
    reservations[selectedTable.id] = reservation;
    localStorage.setItem('tableReservations', JSON.stringify(reservations));
    
    // Update table status visually
    const table = document.querySelector(`[data-table="${selectedTable.id}"]`);
    table.classList.remove('available', 'selected');
    table.classList.add('reserved');
    
    // Show confirmation modal
    showConfirmationModal(reservation);
}

function showConfirmationModal(reservation) {
    const modal = document.getElementById('confirmation-modal');
    
    // Populate confirmation details
    document.getElementById('confirm-table').textContent = `Table ${reservation.tableNumber}`;
    document.getElementById('confirm-name').textContent = reservation.name;
    document.getElementById('confirm-datetime').textContent = `${formatDate(reservation.date)} at ${formatTime(reservation.time)}`;
    document.getElementById('confirm-party-size').textContent = `${reservation.partySize} ${reservation.partySize === '1' ? 'person' : 'people'}`;
    document.getElementById('confirm-phone').textContent = reservation.phone;
    document.getElementById('confirm-email').textContent = reservation.email;
    
    modal.style.display = 'block';
}

// ===== UTILITY FUNCTIONS =====
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

// ===== NAVIGATION =====
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            if (navToggle) {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });
}

// ===== GLOBAL FUNCTIONS (called from HTML) =====
window.openReservationModal = openReservationModal;
window.closeReservationModal = closeReservationModal;
window.closeConfirmationModal = closeConfirmationModal;
window.clearSelection = clearSelection;
