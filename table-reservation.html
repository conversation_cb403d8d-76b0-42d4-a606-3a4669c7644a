<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Reservation - Taste Haven</title>
    <meta name="description" content="Reserve your preferred table at Taste Haven with our interactive table selection system">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/table-reservation.css">
    <link rel="stylesheet" href="css/responsive.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2><a href="index.html" style="color: inherit; text-decoration: none;">Taste Haven</a></h2>
            </div>
            <div class="nav-menu" id="nav-menu">
                <a href="index.html#home" class="nav-link">Home</a>
                <a href="index.html#menu" class="nav-link">Menu</a>
                <a href="table-reservation.html" class="nav-link active">Table Reservation</a>
                <a href="index.html#gallery" class="nav-link">Gallery</a>
                <a href="index.html#contact" class="nav-link">Contact</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Header Section -->
    <section class="reservation-header">
        <div class="container">
            <h1>Interactive Table Reservation</h1>
            <p>Select your preferred table from our restaurant layout below</p>
        </div>
    </section>

    <!-- Legend Section -->
    <section class="legend-section">
        <div class="container">
            <div class="legend">
                <h3>Table Status Legend</h3>
                <div class="legend-items">
                    <div class="legend-item">
                        <div class="legend-color available"></div>
                        <span>Available</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color selected"></div>
                        <span>Selected</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color reserved"></div>
                        <span>Reserved</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color occupied"></div>
                        <span>Currently Occupied</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Restaurant Layout Section -->
    <section class="restaurant-layout-section">
        <div class="container">
            <div class="layout-container">
                <div class="restaurant-floor" id="restaurant-floor">
                    <!-- Entrance -->
                    <div class="entrance">
                        <i class="fas fa-door-open"></i>
                        <span>Entrance</span>
                    </div>

                    <!-- Bar Area -->
                    <div class="bar-area">
                        <div class="bar-counter"></div>
                        <span class="area-label">Bar</span>
                        <!-- Bar Tables -->
                        <div class="table bar-table available" data-table="B1" data-seats="2" data-type="Bar Table">
                            <span class="table-number">B1</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table bar-table available" data-table="B2" data-seats="2" data-type="Bar Table">
                            <span class="table-number">B2</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table bar-table available" data-table="B3" data-seats="2" data-type="Bar Table">
                            <span class="table-number">B3</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table bar-table available" data-table="B4" data-seats="2" data-type="Bar Table">
                            <span class="table-number">B4</span>
                            <div class="seats">2</div>
                        </div>
                    </div>

                    <!-- Main Dining Area -->
                    <div class="main-dining">
                        <span class="area-label">Main Dining</span>
                        
                        <!-- Row 1 -->
                        <div class="table round-table available" data-table="1" data-seats="4" data-type="Round Table">
                            <span class="table-number">1</span>
                            <div class="seats">4</div>
                        </div>
                        <div class="table round-table reserved" data-table="2" data-seats="4" data-type="Round Table">
                            <span class="table-number">2</span>
                            <div class="seats">4</div>
                        </div>
                        <div class="table round-table available" data-table="3" data-seats="4" data-type="Round Table">
                            <span class="table-number">3</span>
                            <div class="seats">4</div>
                        </div>
                        <div class="table round-table available" data-table="4" data-seats="4" data-type="Round Table">
                            <span class="table-number">4</span>
                            <div class="seats">4</div>
                        </div>

                        <!-- Row 2 -->
                        <div class="table square-table available" data-table="5" data-seats="2" data-type="Square Table">
                            <span class="table-number">5</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table square-table occupied" data-table="6" data-seats="2" data-type="Square Table">
                            <span class="table-number">6</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table square-table available" data-table="7" data-seats="2" data-type="Square Table">
                            <span class="table-number">7</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table square-table available" data-table="8" data-seats="2" data-type="Square Table">
                            <span class="table-number">8</span>
                            <div class="seats">2</div>
                        </div>

                        <!-- Row 3 -->
                        <div class="table round-table available" data-table="9" data-seats="6" data-type="Round Table">
                            <span class="table-number">9</span>
                            <div class="seats">6</div>
                        </div>
                        <div class="table round-table available" data-table="10" data-seats="6" data-type="Round Table">
                            <span class="table-number">10</span>
                            <div class="seats">6</div>
                        </div>
                        <div class="table round-table reserved" data-table="11" data-seats="6" data-type="Round Table">
                            <span class="table-number">11</span>
                            <div class="seats">6</div>
                        </div>
                    </div>

                    <!-- Window Side Area -->
                    <div class="window-side">
                        <div class="window-decoration">
                            <i class="fas fa-sun"></i>
                            <span class="area-label">Window Side</span>
                        </div>
                        
                        <div class="table window-table available" data-table="W1" data-seats="2" data-type="Window Table" data-special="Window View">
                            <span class="table-number">W1</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table window-table available" data-table="W2" data-seats="2" data-type="Window Table" data-special="Window View">
                            <span class="table-number">W2</span>
                            <div class="seats">2</div>
                        </div>
                        <div class="table window-table available" data-table="W3" data-seats="4" data-type="Window Table" data-special="Window View">
                            <span class="table-number">W3</span>
                            <div class="seats">4</div>
                        </div>
                        <div class="table window-table available" data-table="W4" data-seats="4" data-type="Window Table" data-special="Window View">
                            <span class="table-number">W4</span>
                            <div class="seats">4</div>
                        </div>
                    </div>

                    <!-- Private Dining Area -->
                    <div class="private-dining">
                        <span class="area-label">Private Dining</span>
                        <div class="table private-table available" data-table="P1" data-seats="8" data-type="Private Table" data-special="Private Room">
                            <span class="table-number">P1</span>
                            <div class="seats">8</div>
                        </div>
                        <div class="table private-table available" data-table="P2" data-seats="10" data-type="Private Table" data-special="Private Room">
                            <span class="table-number">P2</span>
                            <div class="seats">10</div>
                        </div>
                    </div>

                    <!-- Kitchen Area (Visual Only) -->
                    <div class="kitchen-area">
                        <i class="fas fa-utensils"></i>
                        <span class="area-label">Kitchen</span>
                    </div>

                    <!-- Restrooms -->
                    <div class="restrooms">
                        <i class="fas fa-restroom"></i>
                        <span class="area-label">Restrooms</span>
                    </div>
                </div>
            </div>

            <!-- Selected Table Info -->
            <div class="selected-table-info" id="selected-table-info" style="display: none;">
                <div class="info-content">
                    <h3>Selected Table Information</h3>
                    <div class="table-details">
                        <p><strong>Table:</strong> <span id="selected-table-number"></span></p>
                        <p><strong>Seats:</strong> <span id="selected-table-seats"></span></p>
                        <p><strong>Type:</strong> <span id="selected-table-type"></span></p>
                        <p id="selected-table-special" style="display: none;"><strong>Special:</strong> <span id="selected-table-special-text"></span></p>
                    </div>
                    <button class="btn btn-primary" onclick="openReservationModal()">Reserve This Table</button>
                    <button class="btn btn-secondary" onclick="clearSelection()">Clear Selection</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Reservation Modal -->
    <div class="modal reservation-modal" id="reservation-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Reserve Table <span id="modal-table-number"></span></h3>
                <span class="modal-close" onclick="closeReservationModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form class="reservation-form" id="table-reservation-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customer-name">Full Name *</label>
                            <input type="text" id="customer-name" name="name" required>
                            <span class="error-message"></span>
                        </div>
                        <div class="form-group">
                            <label for="customer-phone">Phone Number *</label>
                            <input type="tel" id="customer-phone" name="phone" required>
                            <span class="error-message"></span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="customer-email">Email Address *</label>
                        <input type="email" id="customer-email" name="email" required>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reservation-date">Date *</label>
                            <input type="date" id="reservation-date" name="date" required>
                            <span class="error-message"></span>
                        </div>
                        <div class="form-group">
                            <label for="reservation-time">Time *</label>
                            <select id="reservation-time" name="time" required>
                                <option value="">Select Time</option>
                                <option value="17:00">5:00 PM</option>
                                <option value="17:30">5:30 PM</option>
                                <option value="18:00">6:00 PM</option>
                                <option value="18:30">6:30 PM</option>
                                <option value="19:00">7:00 PM</option>
                                <option value="19:30">7:30 PM</option>
                                <option value="20:00">8:00 PM</option>
                                <option value="20:30">8:30 PM</option>
                                <option value="21:00">9:00 PM</option>
                                <option value="21:30">9:30 PM</option>
                            </select>
                            <span class="error-message"></span>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="party-size">Party Size *</label>
                        <select id="party-size" name="party-size" required>
                            <option value="">Select Party Size</option>
                        </select>
                        <span class="error-message"></span>
                    </div>
                    
                    <div class="form-group">
                        <label for="special-requests">Special Requests</label>
                        <textarea id="special-requests" name="special-requests" rows="3" placeholder="Any dietary restrictions, special occasions, or other requests..."></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeReservationModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Confirm Reservation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal confirmation-modal" id="confirmation-modal">
        <div class="modal-content">
            <div class="modal-header">
                <i class="fas fa-check-circle"></i>
                <h3>Reservation Confirmed!</h3>
            </div>
            <div class="modal-body">
                <div class="confirmation-details">
                    <h4>Reservation Details</h4>
                    <div class="detail-row">
                        <span class="label">Table:</span>
                        <span class="value" id="confirm-table"></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Name:</span>
                        <span class="value" id="confirm-name"></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Date & Time:</span>
                        <span class="value" id="confirm-datetime"></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Party Size:</span>
                        <span class="value" id="confirm-party-size"></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Phone:</span>
                        <span class="value" id="confirm-phone"></span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Email:</span>
                        <span class="value" id="confirm-email"></span>
                    </div>
                </div>
                <p class="confirmation-message">Thank you for choosing Taste Haven! We'll send you a confirmation email shortly.</p>
                <button class="btn btn-primary" onclick="closeConfirmationModal()">Close</button>
            </div>
        </div>
    </div>

    <!-- Tooltip -->
    <div class="table-tooltip" id="table-tooltip">
        <div class="tooltip-content">
            <h4 id="tooltip-table"></h4>
            <p id="tooltip-info"></p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/table-reservation.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
